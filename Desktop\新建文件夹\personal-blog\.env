# 个人博客系统环境变量配置

# 数据库连接配置 (MySQL)
# 格式: mysql://用户名:密码@主机:端口/数据库名
# 请根据您的实际情况修改以下配置

# 本地开发环境 MySQL 配置 (使用 Docker)
DATABASE_URL="mysql://root:123456@localhost:3306/personal_blog"

# 如果您使用本地安装的 MySQL，请修改为：
# DATABASE_URL="mysql://root:您的密码@localhost:3306/personal_blog"

# NextAuth.js 配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here-change-in-production"

# 管理员初始账户配置
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"

# 可选: 邮件服务配置 (用于联系表单等)
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT="587"
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-app-password"